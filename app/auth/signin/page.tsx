"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";

export default function SignInPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [status, setStatus] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Basic client-side validation
    if (!email.trim()) {
      setStatus("Please enter your email address.");
      return;
    }

    if (!password.trim()) {
      setStatus("Please enter your password.");
      return;
    }

    setIsLoading(true);
    setStatus("Signing in...");

    try {
      // Use the backend API for login - backend will determine user role
      const response = await apiService.login({
        email,
        password,
      });

      if (response.success && response.data) {
        // The API response structure: response.data contains { token, data: { id, name, email, isActive, createdAt } }
        const loginResponse = response.data;

        if (loginResponse.token && loginResponse.data) {
          setStatus("Welcome back! Redirecting...");

          // Create user object from developer login response
          const user = {
            id: loginResponse.data.id,
            email: loginResponse.data.email,
            name: loginResponse.data.name,
            isVerified: loginResponse.data.isActive || true, // Default to true if not provided
            createdAt: loginResponse.data.createdAt,
          };

          // Login as developer
          login(user, loginResponse.token, "developer");

          // Redirect to developer dashboard
          setTimeout(() => {
            router.push("/dashboard/dashboard");
          }, 1000);
        } else {
          setStatus("Invalid response format. Please try again.");
          setIsLoading(false);
        }
      } else {
        // Handle specific error messages
        const errorMessage = response.error || response.message || "Invalid email or password";

        // Check for verification error
        if (errorMessage.includes("not verified") || errorMessage.includes("verify your account")) {
          setStatus("Please verify your email address before signing in. Check your inbox for the verification email.");
        } else {
          setStatus(errorMessage);
        }
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Login error:', error); // Debug log
      setStatus("Error signing in. Please try again.");
      setIsLoading(false);
    }
  };

  return (
    <main className="min-h-screen flex items-center justify-center bg-white relative overflow-hidden px-4">
      {/* Animated Background Elements */}
      <div className="absolute w-64 h-64 bg-[#7B1FA2]/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute w-80 h-80 bg-[#4A148C]/5 rounded-full blur-3xl animate-float" style={{ top: "20%", left: "10%" }}></div>
      <div className="absolute w-48 h-48 bg-[#7B1FA2]/5 rounded-full blur-3xl animate-bounce-slow" style={{ bottom: "20%", right: "10%" }}></div>

      {/* Login Card */}
      <div className="relative z-10 w-full max-w-md p-8 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-xl space-y-6 transition-all duration-300 hover:shadow-2xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            Sign In to Crefy Connect
          </h1>
          <p className="text-gray-600 mt-2">Welcome back to your account</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isLoading}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={isLoading}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B1FA2] transition-shadow duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              placeholder="••••••••"
            />
          </div>

          {/* Status Message */}
          {status && (
            <p
              className={`text-sm ${
                status.includes("Invalid") ||
                status.includes("Error") ||
                status.includes("failed") ||
                status.includes("Please verify")
                  ? "text-red-500"
                  : status.includes("Welcome") || status.includes("Redirecting")
                  ? "text-green-600"
                  : "text-gray-600"
              }`}
            >
              {status}
            </p>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-3 bg-[#4A148C] text-white rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-md hover:shadow-lg font-medium disabled:opacity-50"
          >
            {isLoading ? "Signing in..." : "Sign In"}
          </button>
        </form>

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{" "}
            <Link href="/auth/signup" className="text-[#4A148C] hover:underline font-medium">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}